<?php

use Botble\PluginManagement\Services\PluginService;
use Illuminate\Contracts\Console\Kernel;

// Script kiểm tra plugin Unlic trên dự án khác

echo "=== KIỂM TRA PLUGIN UNLIC ===\n\n";

// 1. Kiểm tra plugin có tồn tại không
$pluginPath = __DIR__ . '/platform/plugins/unlic';
echo '1. Plugin path exists: ' . ($pluginPath && is_dir($pluginPath) ? '✅ YES' : '❌ NO') . "\n";

// 2. Kiểm tra file chính
$mainFiles = [
    '/src/Plugin.php',
    '/src/Providers/UnlicServiceProvider.php',
    '/src/Http/Middleware/InjectUnlicScript.php',
    '/plugin.json',
];

foreach ($mainFiles as $file) {
    $filePath = $pluginPath . $file;
    echo '   - ' . basename($file) . ': ' . (file_exists($filePath) ? '✅' : '❌') . "\n";
}

// 3. <PERSON><PERSON>m tra plugin.json
$pluginJsonPath = $pluginPath . '/plugin.json';
if (file_exists($pluginJsonPath)) {
    $pluginJson = json_decode(file_get_contents($pluginJsonPath), true);
    echo "\n2. Plugin info:\n";
    echo '   - Name: ' . ($pluginJson['name'] ?? 'N/A') . "\n";
    echo '   - Version: ' . ($pluginJson['version'] ?? 'N/A') . "\n";
    echo '   - Provider: ' . ($pluginJson['provider'] ?? 'N/A') . "\n";
}

// 4. Kiểm tra trong database (nếu có kết nối)
try {
    if (file_exists(__DIR__ . '/vendor/autoload.php')) {
        require_once __DIR__ . '/vendor/autoload.php';

        // Load Laravel app
        $app = require_once __DIR__ . '/bootstrap/app.php';
        $kernel = $app->make(Kernel::class);
        $kernel->bootstrap();

        // Kiểm tra plugin trong database
        $activatedPlugins = PluginService::getActivatedPlugins();
        echo "\n3. Plugin activation status:\n";
        echo '   - Activated: ' . (in_array('unlic', $activatedPlugins) ? '✅ YES' : '❌ NO') . "\n";

        // Kiểm tra middleware
        $middlewares = app('router')->getMiddleware();
        $webMiddlewares = app('router')->getMiddlewareGroups()['web'] ?? [];

        echo "\n4. Middleware registration:\n";
        echo '   - InjectUnlicScript alias: ' . (isset($middlewares['unlic.inject']) ? '✅ YES' : '❌ NO') . "\n";

        // Kiểm tra middleware trong web group
        $injectFound = false;
        foreach ($webMiddlewares as $middleware) {
            if (is_string($middleware) && strpos($middleware, 'InjectUnlicScript') !== false) {
                $injectFound = true;

                break;
            }
        }
        echo '   - InjectUnlicScript in web group: ' . ($injectFound ? '✅ YES' : '❌ NO') . "\n";

    } else {
        echo "\n3. Cannot check database - Laravel not loaded\n";
    }
} catch (Exception $e) {
    echo "\n3. Error checking database: " . $e->getMessage() . "\n";
}

echo "\n=== HƯỚNG DẪN SỬA LỖI ===\n";
echo "Nếu plugin chưa hoạt động:\n";
echo "1. php artisan cms:plugin:activate unlic\n";
echo "2. php artisan cache:clear\n";
echo "3. php artisan config:clear\n";
echo "4. php artisan cms:unlic:test\n";
