<style>
/* Ẩn ngay lập tức bằng CSS */
.alert-license,
[data-bb-toggle="authorized-reminder"],
.alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
div[role="alert"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}
</style>

<script>
// Script đơn giản để ẩn ngay lập tức
(function() {
    function hideNow() {
        // Ẩn theo class và attribute
        const elements = document.querySelectorAll('.alert-license, [data-bb-toggle="authorized-reminder"]');
        elements.forEach(function(el) {
            el.style.display = 'none';
            el.style.visibility = 'hidden';
            el.style.opacity = '0';
            el.style.height = '0';
            el.style.position = 'absolute';
            el.style.left = '-9999px';
            el.style.top = '-9999px';
            el.remove(); // Xóa luôn
        });
        
        // Ẩn theo text content
        const allDivs = document.querySelectorAll('div[role="alert"]');
        allDivs.forEach(function(div) {
            if (div.textContent && div.textContent.includes('Your license is invalid')) {
                div.style.display = 'none';
                div.remove();
            }
        });
    }
    
    // Chạy ngay lập tức
    hideNow();
    
    // Chạy khi DOM ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', hideNow);
    } else {
        hideNow();
    }
    
    // Chạy định kỳ
    setInterval(hideNow, 500);
    
    console.log('Unlic Simple: License alert hidden');
})();
</script>
