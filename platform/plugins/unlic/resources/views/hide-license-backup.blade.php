<script>
// Backup script để ẩn license notifications
(function() {
    'use strict';
    
    // Chỉ chạy nếu script ch<PERSON>h chưa chạy
    if (window.unlicPluginLoaded) {
        return;
    }
    
    const licenseSelectors = [
        '.alert-license',
        '[data-bb-toggle="authorized-reminder"]',
        '#quick-activation-license-modal',
        'form[data-bb-toggle="activate-license"]',
        '.license-activation-modal'
    ];
    
    function hideElements() {
        licenseSelectors.forEach(selector => {
            try {
                document.querySelectorAll(selector).forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
            } catch (e) {
                // Ignore errors
            }
        });
    }
    
    hideElements();
    
    // Chạy lại sau 1 giây
    setTimeout(hideElements, 1000);
})();
</script>
