<style>
    /* CSS để ẩn các thông báo license */
    .alert-license,
    [data-bb-toggle="authorized-reminder"],
    #quick-activation-license-modal,
    form[data-bb-toggle="activate-license"],
    form[action*="license/activate"],
    .btn-close[data-bs-target="#quick-activation-license-modal"],
    a[href*="license.botble.com"],
    a[href*="codecanyon.net"],
    a[href*="help.market.envato.com"],
    .alert-sticky.bg-warning.text-white,
    .vertical-wrapper.alert-license,
    .resume-setup-wizard-wrapper,
    [data-reload="true"][data-bb-toggle="activate-license"],
    .license-activation-modal,
    #license-form,
    v-license-form,
    [id*="license"],
    [class*="license"],
    [data-bs-target*="license"],
    [href*="license"],
    .alert.alert-warning:has(a[href*="Settings"]),
    .alert:has([href*="license"]),
    .modal:has([data-bb-toggle="activate-license"]) {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        width: 0 !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        overflow: hidden !important;
        position: absolute !important;
        left: -9999px !important;
        top: -9999px !important;
    }

    /* Ẩn các thông báo có nội dung liên quan đến license */
    .alert:has-text("license"),
    .alert:has-text("License"),
    .alert:has-text("activate"),
    .alert:has-text("Activate"),
    .alert:has-text("invalid"),
    .alert:has-text("Invalid") {
        display: none !important;
    }
</style>

<script>
    (function() {
        'use strict';

        // Danh sách các selector cần ẩn
        const licenseSelectors = [
            '.alert-license',
            '[data-bb-toggle="authorized-reminder"]',
            '#quick-activation-license-modal',
            'form[data-bb-toggle="activate-license"]',
            'form[action*="license/activate"]',
            '.btn-close[data-bs-target="#quick-activation-license-modal"]',
            'a[href*="license.botble.com"]',
            'a[href*="codecanyon.net"]',
            'a[href*="help.market.envato.com"]',
            '.alert-sticky.bg-warning.text-white',
            '.vertical-wrapper.alert-license',
            '.resume-setup-wizard-wrapper',
            '[data-reload="true"][data-bb-toggle="activate-license"]',
            '.license-activation-modal',
            '#license-form',
            'v-license-form',
            '[id*="license"]',
            '[class*="license"]',
            '[data-bs-target*="license"]',
            '[href*="license"]',
            '.modal:has([data-bb-toggle="activate-license"])'
        ];

        // Từ khóa để tìm trong nội dung text
        const licenseKeywords = [
            'license',
            'License',
            'LICENSE',
            'activate',
            'Activate',
            'ACTIVATE',
            'invalid',
            'Invalid',
            'INVALID',
            'Your license is invalid',
            'activate license',
            'Activate license',
            'license activation',
            'License Activation',
            'Requires License Activation',
            'codecanyon',
            'envato'
        ];

        // Hàm ẩn element
        function hideElement(element) {
            if (element && element.style) {
                element.style.setProperty('display', 'none', 'important');
                element.style.setProperty('visibility', 'hidden', 'important');
                element.style.setProperty('opacity', '0', 'important');
                element.style.setProperty('height', '0', 'important');
                element.style.setProperty('width', '0', 'important');
                element.style.setProperty('margin', '0', 'important');
                element.style.setProperty('padding', '0', 'important');
                element.style.setProperty('border', 'none', 'important');
                element.style.setProperty('overflow', 'hidden', 'important');
                element.style.setProperty('position', 'absolute', 'important');
                element.style.setProperty('left', '-9999px', 'important');
                element.style.setProperty('top', '-9999px', 'important');

                // Thêm class để đánh dấu đã ẩn
                element.classList.add('unlic-hidden');

                // Ngăn chặn event
                element.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }, true);
            }
        }

        // Hàm ẩn theo selector
        function hideBySelectors() {
            licenseSelectors.forEach(selector => {
                try {
                    document.querySelectorAll(selector).forEach(hideElement);
                } catch (e) {
                    // Ignore invalid selectors
                }
            });
        }

        // Hàm ẩn theo nội dung text
        function hideByTextContent() {
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                if (element.classList && element.classList.contains('unlic-hidden')) {
                    return; // Đã ẩn rồi
                }

                const textContent = element.textContent || element.innerText || '';
                const hasLicenseKeyword = licenseKeywords.some(keyword =>
                    textContent.toLowerCase().includes(keyword.toLowerCase())
                );

                if (hasLicenseKeyword) {
                    // Kiểm tra xem có phải là alert hoặc modal không
                    if (element.classList.contains('alert') ||
                        element.classList.contains('modal') ||
                        element.tagName === 'FORM' ||
                        element.closest('.alert') ||
                        element.closest('.modal')) {
                        hideElement(element);
                    }
                }
            });
        }

        // Hàm chính để ẩn tất cả
        function hideLicenseElements() {
            hideBySelectors();
            hideByTextContent();
        }

        // Chạy ngay khi script load
        hideLicenseElements();

        // Chạy khi DOM ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', hideLicenseElements);
        }

        // Chạy khi window load
        window.addEventListener('load', hideLicenseElements);

        // Sử dụng MutationObserver để theo dõi các thay đổi DOM
        const observer = new MutationObserver(function(mutations) {
            let shouldCheck = false;

            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                    shouldCheck = true;
                }
            });

            if (shouldCheck) {
                // Delay một chút để đảm bảo DOM đã render xong
                setTimeout(hideLicenseElements, 100);
            }
        });

        // Bắt đầu observe
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class', 'id', 'data-bb-toggle', 'data-bs-target']
        });

        // Chạy định kỳ để đảm bảo
        setInterval(hideLicenseElements, 2000);

        // Override các hàm có thể hiển thị license modal
        const originalShowModal = window.bootstrap?.Modal?.prototype?.show;
        if (originalShowModal) {
            window.bootstrap.Modal.prototype.show = function() {
                const modalElement = this._element;
                if (modalElement && (
                        modalElement.id.includes('license') ||
                        modalElement.querySelector('[data-bb-toggle="activate-license"]') ||
                        modalElement.textContent.toLowerCase().includes('license')
                    )) {
                    return; // Không hiển thị modal license
                }
                return originalShowModal.apply(this, arguments);
            };
        }

        // Override alert functions
        const originalAlert = window.alert;
        window.alert = function(message) {
            if (typeof message === 'string' &&
                licenseKeywords.some(keyword => message.toLowerCase().includes(keyword.toLowerCase()))) {
                return; // Không hiển thị alert license
            }
            return originalAlert.apply(this, arguments);
        };

        // Đánh dấu script đã load
        window.unlicPluginLoaded = true;

        console.log('Unlic Plugin: License notifications hidden successfully');
    })();
</script>
