<?php

namespace Bo<PERSON>ble\Unlic;

use Botble\PluginManagement\Abstracts\PluginOperationAbstract;

class Plugin extends PluginOperationAbstract
{
    public static function activate(): void
    {
        // Plugin activation logic
        // Clear cache to ensure changes take effect
        if (function_exists('cache_clear')) {
            cache_clear();
        }
    }

    public static function deactivate(): void
    {
        // Plugin deactivation logic
        // Clear cache
        if (function_exists('cache_clear')) {
            cache_clear();
        }
    }

    public static function remove(): void
    {
        // Plugin removal logic
        // Clear cache
        if (function_exists('cache_clear')) {
            cache_clear();
        }
    }
}
