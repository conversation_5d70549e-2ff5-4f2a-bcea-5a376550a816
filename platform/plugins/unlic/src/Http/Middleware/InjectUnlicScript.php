<?php

namespace Botble\Unlic\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InjectUnlicScript
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Only inject in admin area HTML responses
        if ($this->shouldInject($request, $response)) {
            $content = $response->getContent();
            
            // Inject CSS in head
            $cssScript = $this->getCssScript();
            $content = str_replace('</head>', $cssScript . '</head>', $content);
            
            // Inject JS before closing body
            $jsScript = $this->getJsScript();
            $content = str_replace('</body>', $jsScript . '</body>', $content);
            
            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Determine if we should inject the script
     */
    private function shouldInject(Request $request, $response): bool
    {
        // Only for admin routes
        if (!$request->is('admin*') && !$request->is('*/admin*')) {
            return false;
        }

        // Only for HTML responses
        if (!$response instanceof Response) {
            return false;
        }

        $contentType = $response->headers->get('Content-Type', '');
        if (strpos($contentType, 'text/html') === false && empty($contentType)) {
            return false;
        }

        return true;
    }

    /**
     * Get CSS script to hide license elements
     */
    private function getCssScript(): string
    {
        return '
<style id="unlic-css-hide">
.alert-license,
[data-bb-toggle="authorized-reminder"],
#quick-activation-license-modal,
form[data-bb-toggle="activate-license"],
.license-activation-modal,
[id*="license"],
[class*="license"],
.alert.alert-warning.alert-sticky.bg-warning.text-white,
.vertical-wrapper.alert-license.alert-important,
.alert-sticky.small.bg-warning.text-white {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}
</style>';
    }

    /**
     * Get JavaScript to hide license elements
     */
    private function getJsScript(): string
    {
        return '
<script id="unlic-js-hide">
(function() {
    "use strict";
    
    function hideElements() {
        const selectors = [
            ".alert-license",
            "[data-bb-toggle=\"authorized-reminder\"]",
            "#quick-activation-license-modal",
            ".alert.alert-warning.alert-sticky.bg-warning.text-white",
            ".vertical-wrapper.alert-license.alert-important",
            ".alert-sticky.small.bg-warning.text-white"
        ];
        
        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                el.style.setProperty("display", "none", "important");
                el.style.setProperty("visibility", "hidden", "important");
                el.style.setProperty("opacity", "0", "important");
                el.style.setProperty("height", "0", "important");
                el.style.setProperty("position", "absolute", "important");
                el.style.setProperty("left", "-9999px", "important");
                el.style.setProperty("top", "-9999px", "important");
                el.remove();
            });
        });
    }
    
    // Run immediately
    hideElements();
    
    // Run when DOM ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", hideElements);
    }
    
    // Run periodically
    setInterval(hideElements, 1000);
    
    console.log("Unlic: License elements hidden");
})();
</script>';
    }
}
