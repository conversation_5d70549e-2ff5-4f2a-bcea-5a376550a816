<?php

namespace Botble\Unlic\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InjectUnlicScript
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Debug log
        \Log::info('Unlic middleware running for: ' . $request->url());

        // Check if this is an unlicensed redirect
        if ($this->isUnlicensedRedirect($request)) {
            return $this->handleUnlicensedRedirect($request);
        }

        $response = $next($request);

        // Only inject in admin area HTML responses
        if ($this->shouldInject($request, $response)) {
            $content = $response->getContent();

            // Remove license alerts from HTML directly
            $content = $this->removeLicenseAlerts($content);

            // Inject CSS in head
            $cssScript = $this->getCssScript();
            $content = str_replace('</head>', $cssScript . '</head>', $content);

            // Inject JS before closing body
            $jsScript = $this->getJsScript();
            $content = str_replace('</body>', $jsScript . '</body>', $content);

            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Check if this is an unlicensed redirect
     */
    private function isUnlicensedRedirect(Request $request): bool
    {
        return $request->is('*/unlicensed') ||
               $request->is('unlicensed') ||
               str_contains($request->getRequestUri(), '/unlicensed');
    }

    /**
     * Handle unlicensed redirect by redirecting to intended page
     */
    private function handleUnlicensedRedirect(Request $request)
    {
        $redirectUrl = $request->get('redirect_url');

        if ($redirectUrl) {
            // Redirect to the intended URL, bypassing license check
            return redirect($redirectUrl);
        }

        // If no redirect URL, go to admin dashboard
        return redirect('/admin');
    }

    /**
     * Determine if we should inject the script
     */
    private function shouldInject(Request $request, $response): bool
    {
        // Only for admin routes
        if (! $request->is('admin*') && ! $request->is('*/admin*')) {
            return false;
        }

        // Only for HTML responses
        if (! $response instanceof Response) {
            return false;
        }

        $contentType = $response->headers->get('Content-Type', '');
        if (strpos($contentType, 'text/html') === false && empty($contentType)) {
            return false;
        }

        return true;
    }

    /**
     * Get CSS script to hide license elements
     */
    private function getCssScript(): string
    {
        return '
<style id="unlic-css-hide">
.alert-license,
[data-bb-toggle="authorized-reminder"],
#quick-activation-license-modal,
form[data-bb-toggle="activate-license"],
.license-activation-modal,
[id*="license"],
[class*="license"],
.alert.alert-warning.alert-sticky.bg-warning.text-white,
.vertical-wrapper.alert-license.alert-important,
.alert-sticky.small.bg-warning.text-white,
div[role="alert"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
div.alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}
</style>';
    }

    /**
     * Get JavaScript to hide license elements
     */
    private function getJsScript(): string
    {
        return '
<script id="unlic-js-hide">
(function() {
    "use strict";

    function hideElements() {
        const selectors = [
            ".alert-license",
            "[data-bb-toggle=\"authorized-reminder\"]",
            "#quick-activation-license-modal",
            ".alert.alert-warning.alert-sticky.bg-warning.text-white",
            ".vertical-wrapper.alert-license.alert-important",
            ".alert-sticky.small.bg-warning.text-white",
            "div[role=\"alert\"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important",
            ".alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important"
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                el.style.setProperty("display", "none", "important");
                el.style.setProperty("visibility", "hidden", "important");
                el.style.setProperty("opacity", "0", "important");
                el.style.setProperty("height", "0", "important");
                el.style.setProperty("position", "absolute", "important");
                el.style.setProperty("left", "-9999px", "important");
                el.style.setProperty("top", "-9999px", "important");
                el.remove();
            });
        });

        // Also find elements by text content
        const textSelectors = [
            "Your license is invalid",
            "license is invalid",
            "activate license",
            "License Activation"
        ];

        textSelectors.forEach(text => {
            const xpath = `//div[contains(text(), '${text}')]`;
            const result = document.evaluate(xpath, document, null, XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE, null);
            for (let i = 0; i < result.snapshotLength; i++) {
                const element = result.snapshotItem(i);
                if (element) {
                    // Find the closest alert container
                    const alertContainer = element.closest('.alert') || element.closest('[role="alert"]') || element;
                    alertContainer.remove();
                }
            }
        });
    }

    // Run immediately
    hideElements();

    // Run when DOM ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", hideElements);
    }

    // Run periodically
    setInterval(hideElements, 1000);

    console.log("Unlic: License elements hidden");
})();
</script>';
    }

    /**
     * Remove license alerts from HTML content
     */
    private function removeLicenseAlerts(string $content): string
    {
        // Remove the specific alert div
        $patterns = [
            // Remove the entire alert div with all its classes
            '/<div[^>]*role="alert"[^>]*class="[^"]*alert-warning[^"]*alert-license[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*class="[^"]*alert[^"]*alert-warning[^"]*alert-license[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*data-bb-toggle="authorized-reminder"[^>]*>.*?<\/div>/s',

            // Remove modal
            '/<div[^>]*id="quick-activation-license-modal"[^>]*>.*?<\/div>/s',

            // Remove by text content
            '/<div[^>]*>[^<]*Your license is invalid[^<]*<\/div>/s',
        ];

        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // More aggressive removal - find and remove any div containing license text
        $content = preg_replace_callback(
            '/<div[^>]*>(.*?)<\/div>/s',
            function ($matches) {
                $divContent = $matches[1];
                if (stripos($divContent, 'license is invalid') !== false ||
                    stripos($divContent, 'activate license') !== false ||
                    stripos($divContent, 'License Activation') !== false) {
                    return ''; // Remove the entire div
                }
                return $matches[0]; // Keep the div
            },
            $content
        );

        return $content;
    }
}
