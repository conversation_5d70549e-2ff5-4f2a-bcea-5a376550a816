<?php

namespace Botble\Unlic\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class InjectUnlicScript
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Debug log
        \Log::info('Unlic middleware running for: ' . $request->url());

        // Check if this is an unlicensed redirect
        if ($this->isUnlicensedRedirect($request)) {
            return $this->handleUnlicensedRedirect($request);
        }

        $response = $next($request);

        // Only inject in admin area HTML responses
        \Log::info('Unlic middleware running for: ' . $request->url());
        if ($this->shouldInject($request, $response)) {
            \Log::info('Unlic middleware processing: ' . $request->url());
            $content = $response->getContent();

            // Remove license alerts from HTML directly
            $content = $this->removeLicenseAlerts($content);
            \Log::info('Unlic: License content removed from HTML');

            // Inject CSS in head
            $cssScript = $this->getCssScript();
            $content = str_replace('</head>', $cssScript . '</head>', $content);

            // Inject JS before closing body
            $jsScript = $this->getJsScript();
            $content = str_replace('</body>', $jsScript . '</body>', $content);

            $response->setContent($content);
        }

        return $response;
    }

    /**
     * Check if this is an unlicensed redirect
     */
    private function isUnlicensedRedirect(Request $request): bool
    {
        return $request->is('*/unlicensed') ||
               $request->is('unlicensed') ||
               str_contains($request->getRequestUri(), '/unlicensed');
    }

    /**
     * Handle unlicensed redirect by redirecting to intended page
     */
    private function handleUnlicensedRedirect(Request $request)
    {
        $redirectUrl = $request->get('redirect_url');

        if ($redirectUrl) {
            // Redirect to the intended URL, bypassing license check
            return redirect($redirectUrl);
        }

        // If no redirect URL, go to admin dashboard
        return redirect('/admin');
    }

    /**
     * Determine if we should inject the script
     */
    private function shouldInject(Request $request, $response): bool
    {
        // Only for admin routes (support different admin prefixes)
        if (! $request->is('admin*') && ! $request->is('*/admin*') &&
            ! $request->is('vigadmin*') && ! $request->is('*/vigadmin*')) {
            return false;
        }

        // Only for HTML responses
        if (! $response instanceof Response) {
            return false;
        }

        $contentType = $response->headers->get('Content-Type', '');
        if (strpos($contentType, 'text/html') === false && empty($contentType)) {
            return false;
        }

        return true;
    }

    /**
     * Get CSS script to hide license elements
     */
    private function getCssScript(): string
    {
        return '
<style id="unlic-css-hide">
.alert-license,
[data-bb-toggle="authorized-reminder"],
#quick-activation-license-modal,
form[data-bb-toggle="activate-license"],
.license-activation-modal,
[id*="license"],
[class*="license"],
.alert.alert-warning.alert-sticky.bg-warning.text-white,
.vertical-wrapper.alert-license.alert-important,
.alert-sticky.small.bg-warning.text-white,
div[role="alert"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important,
div.alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    height: 0 !important;
    width: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
    border: none !important;
    overflow: hidden !important;
    position: absolute !important;
    left: -9999px !important;
    top: -9999px !important;
    z-index: -1 !important;
}
</style>';
    }

    /**
     * Get JavaScript to hide license elements
     */
    private function getJsScript(): string
    {
        return '
<script id="unlic-js-hide">
(function() {
    "use strict";

    function hideElements() {
        const selectors = [
            ".alert-license",
            "[data-bb-toggle=\"authorized-reminder\"]",
            "#quick-activation-license-modal",
            ".alert.alert-warning.alert-sticky.bg-warning.text-white",
            ".vertical-wrapper.alert-license.alert-important",
            ".alert-sticky.small.bg-warning.text-white",
            "div[role=\"alert\"].alert.alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important",
            ".alert-warning.alert-license.alert-sticky.small.bg-warning.text-white.vertical-wrapper.alert-important"
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(el => {
                el.style.setProperty("display", "none", "important");
                el.style.setProperty("visibility", "hidden", "important");
                el.style.setProperty("opacity", "0", "important");
                el.style.setProperty("height", "0", "important");
                el.style.setProperty("position", "absolute", "important");
                el.style.setProperty("left", "-9999px", "important");
                el.style.setProperty("top", "-9999px", "important");
                el.remove();
            });
        });

        // Also find elements by text content (safe selectors only)
        const textSelectors = [
            "Your license is invalid",
            "License Activation",
            "purchase code (license) is only valid for One Domain"
        ];

        textSelectors.forEach(function(text) {
            const xpath = "//div[contains(text(), \'" + text + "\')]";
            const result = document.evaluate(xpath, document, null, XPathResult.UNORDERED_NODE_SNAPSHOT_TYPE, null);
            for (let i = 0; i < result.snapshotLength; i++) {
                const element = result.snapshotItem(i);
                if (element) {
                    // Find the closest alert container or the element itself
                    const alertContainer = element.closest(".alert") || element.closest("[role=\"alert\"]") || element;
                    alertContainer.remove();
                }
            }
        });

        // Also find and remove paragraphs containing specific license text
        const paragraphs = document.querySelectorAll("p");
        paragraphs.forEach(function(p) {
            const text = p.textContent || p.innerText;
            if (text.includes("purchase code") && text.includes("license") && text.includes("One Domain")) {
                // Remove the parent div if it only contains this paragraph
                const parentDiv = p.parentElement;
                if (parentDiv && parentDiv.tagName === "DIV" && parentDiv.children.length === 1) {
                    parentDiv.remove();
                } else {
                    p.remove();
                }
            }
        });

        // Remove form elements related to license activation
        const licenseForm = document.querySelector("form[data-bb-toggle=\\"activate-license\\"]");
        if (licenseForm) {
            licenseForm.remove();
        }

        // Remove form fields with specific content
        const formLabels = document.querySelectorAll("label");
        formLabels.forEach(function(label) {
            const text = label.textContent || label.innerText;
            if (text.includes("Your username on Envato") ||
                text.includes("Purchase code") ||
                text.includes("license entitles one person")) {
                // Remove the parent form group
                const formGroup = label.closest(".mb-3") || label.closest(".form-group");
                if (formGroup) {
                    formGroup.remove();
                }
            }
        });

        // Remove buttons with license activation text
        const buttons = document.querySelectorAll("button");
        buttons.forEach(function(btn) {
            const text = btn.textContent || btn.innerText;
            if (text.includes("Activate license")) {
                const formGroup = btn.closest(".mb-3") || btn.closest(".form-group");
                if (formGroup) {
                    formGroup.remove();
                } else {
                    btn.remove();
                }
            }
        });

        // Remove modal close buttons
        const modalCloseButtons = document.querySelectorAll("a[data-bs-target=\\"#quick-activation-license-modal\\"]");
        modalCloseButtons.forEach(function(btn) {
            btn.remove();
        });

        // Remove any btn-close targeting license modal
        const closeButtons = document.querySelectorAll("a.btn-close");
        closeButtons.forEach(function(btn) {
            const target = btn.getAttribute("data-bs-target");
            if (target && target.includes("license")) {
                btn.remove();
            }
        });
    }

    // Run immediately
    hideElements();

    // Run when DOM ready
    if (document.readyState === "loading") {
        document.addEventListener("DOMContentLoaded", hideElements);
    }

    // Run periodically
    setInterval(hideElements, 1000);
})();
</script>';
    }

    /**
     * Remove license alerts from HTML content
     */
    private function removeLicenseAlerts(string $content): string
    {
        // Safe and specific removal patterns only
        $patterns = [
            // Remove the specific alert div with exact classes
            '/<div[^>]*role="alert"[^>]*class="[^"]*alert-warning[^"]*alert-license[^"]*alert-sticky[^"]*"[^>]*>.*?<\/div>/s',
            '/<div[^>]*data-bb-toggle="authorized-reminder"[^>]*>.*?<\/div>/s',

            // Remove modal completely - more comprehensive
            '/<div[^>]*class="[^"]*modal[^"]*"[^>]*id="quick-activation-license-modal"[^>]*>.*?<\/div>\s*<\/div>\s*<\/div>/s',
            '/<div[^>]*id="quick-activation-license-modal"[^>]*>.*?<\/div>\s*<\/div>\s*<\/div>/s',

            // Remove form elements with license content
            '/<form[^>]*data-bb-toggle="activate-license"[^>]*>.*?<\/form>/s',
            '/<div[^>]*class="[^"]*modal-body[^"]*"[^>]*>.*?activate-license.*?<\/div>/s',

            // Remove specific license text paragraphs
            '/<div[^>]*>\s*<p[^>]*>\s*A purchase code \(license\) is only valid for One Domain.*?<\/p>\s*<\/div>/s',
            '/<div[^>]*>\s*<p[^>]*>.*?purchase code.*?license.*?One Domain.*?<\/p>\s*<\/div>/s',
            '/<p[^>]*>.*?purchase code.*?license.*?One Domain.*?<\/p>/s',

            // Remove form fields related to license
            '/<div[^>]*class="[^"]*mb-3[^"]*"[^>]*>.*?Your username on Envato.*?<\/div>/s',
            '/<div[^>]*class="[^"]*mb-3[^"]*"[^>]*>.*?Purchase code.*?<\/div>/s',
            '/<div[^>]*class="[^"]*mb-3[^"]*"[^>]*>.*?Activate license.*?<\/div>/s',
            '/<div[^>]*class="[^"]*mb-3[^"]*"[^>]*>.*?license entitles one person.*?<\/div>/s',

            // Remove links to license sites
            '/<a[^>]*href="[^"]*license\.botble\.com[^"]*"[^>]*>.*?<\/a>/s',
            '/<a[^>]*href="[^"]*codecanyon\.net[^"]*"[^>]*>.*?<\/a>/s',

            // Remove modal close button
            '/<a[^>]*class="[^"]*btn-close[^"]*"[^>]*data-bs-target="#quick-activation-license-modal"[^>]*>.*?<\/a>/s',
            '/<a[^>]*data-bs-target="#quick-activation-license-modal"[^>]*>.*?<\/a>/s',
        ];

        foreach ($patterns as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        return $content;
    }
}
