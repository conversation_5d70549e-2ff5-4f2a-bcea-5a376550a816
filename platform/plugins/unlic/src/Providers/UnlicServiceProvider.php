<?php

namespace Bo<PERSON>ble\Unlic\Providers;

use Botble\Base\Supports\ServiceProvider as BaseServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Unlic\Http\Middleware\InjectUnlicScript;

class UnlicServiceProvider extends BaseServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->setNamespace('plugins/unlic');

        // Load views
        $this->loadAndPublishViews();

        // Register middleware for admin routes
        $this->app['router']->pushMiddlewareToGroup('web', InjectUnlicScript::class);

        // Inject license hiding script immediately
        $this->injectLicenseHiding();

        // Hook into various events to ensure script is loaded
        add_action('init', [$this, 'injectLicenseHiding']);

        // Add filters for different injection points
        add_filter('base_filter_before_head_close', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        add_filter('base_filter_after_body_tag', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        // Also try admin_head hook
        add_action('admin_head', function () {
            echo view('plugins/unlic::simple-hide')->render();
        });

        // And admin_footer
        add_action('admin_footer', function () {
            echo view('plugins/unlic::simple-hide')->render();
        });
    }

    public function register()
    {
        // Register any services if needed
    }

    /**
     * Inject license hiding functionality
     */
    public function injectLicenseHiding(): void
    {
        // Only inject in web requests, not CLI
        if (app()->runningInConsole() || headers_sent()) {
            return;
        }

        // Add immediate CSS to hide license elements
        if (request()->is('admin*') || request()->is('*/admin*')) {
            echo '<style id="unlic-immediate-hide">
                .alert-license,
                [data-bb-toggle="authorized-reminder"],
                #quick-activation-license-modal,
                form[data-bb-toggle="activate-license"],
                .license-activation-modal,
                [id*="license"],
                [class*="license"],
                .alert.alert-warning.alert-sticky.bg-warning.text-white,
                .vertical-wrapper.alert-license.alert-important {
                    display: none !important;
                    visibility: hidden !important;
                    opacity: 0 !important;
                    height: 0 !important;
                    width: 0 !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    border: none !important;
                    overflow: hidden !important;
                    position: absolute !important;
                    left: -9999px !important;
                    top: -9999px !important;
                    z-index: -1 !important;
                }
            </style>
            <script>
                // Immediate hiding script
                (function() {
                    function hideNow() {
                        const selectors = [
                            ".alert-license",
                            "[data-bb-toggle=\"authorized-reminder\"]",
                            "#quick-activation-license-modal",
                            ".alert.alert-warning.alert-sticky.bg-warning.text-white",
                            ".vertical-wrapper.alert-license.alert-important"
                        ];

                        selectors.forEach(selector => {
                            document.querySelectorAll(selector).forEach(el => {
                                el.style.setProperty("display", "none", "important");
                                el.style.setProperty("visibility", "hidden", "important");
                                el.style.setProperty("opacity", "0", "important");
                                el.style.setProperty("height", "0", "important");
                                el.style.setProperty("position", "absolute", "important");
                                el.style.setProperty("left", "-9999px", "important");
                                el.style.setProperty("top", "-9999px", "important");
                                el.remove(); // Xóa luôn element
                            });
                        });
                    }

                    // Chạy ngay lập tức
                    hideNow();

                    // Chạy khi DOM ready
                    if (document.readyState === "loading") {
                        document.addEventListener("DOMContentLoaded", hideNow);
                    }

                    // Chạy định kỳ
                    setInterval(hideNow, 500);
                })();
            </script>';
        }
    }
}
