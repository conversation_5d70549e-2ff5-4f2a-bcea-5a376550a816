<?php

namespace Bo<PERSON>ble\Unlic\Providers;

use Botble\Base\Supports\ServiceProvider as BaseServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Illuminate\Support\Facades\Event;

class UnlicServiceProvider extends BaseServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->setNamespace('plugins/unlic');

        // Load views
        $this->loadAndPublishViews();

        // Inject license hiding script into all admin pages
        Event::listen('core.init', function () {
            // Add to head for early execution
            add_filter('base_filter_before_head_close', function ($html) {
                return $html . view('plugins/unlic::hide-license')->render();
            });

            // Also add after body tag as backup
            add_filter('base_filter_after_body_tag', function ($html) {
                return $html . view('plugins/unlic::hide-license-backup')->render();
            });
        });

        // Hook into admin area specifically
        if (request()->is('admin*') || request()->is('*/admin*')) {
            $this->injectLicenseHiding();
        }
    }

    public function register()
    {
        // Register any services if needed
    }

    /**
     * Inject license hiding functionality
     */
    protected function injectLicenseHiding(): void
    {
        // Add CSS to hide license elements immediately
        add_action('admin_head', function () {
            echo '<style>
                .alert-license,
                [data-bb-toggle="authorized-reminder"],
                #quick-activation-license-modal,
                form[data-bb-toggle="activate-license"],
                .license-activation-modal,
                [id*="license"],
                [class*="license"] {
                    display: none !important;
                    visibility: hidden !important;
                }
            </style>';
        });
    }
}
