<?php

namespace Bo<PERSON>ble\Unlic\Providers;

use Botble\Base\Supports\ServiceProvider as BaseServiceProvider;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Botble\Unlic\Http\Middleware\BypassLicenseCheck;
use Bo<PERSON>ble\Unlic\Http\Middleware\InjectUnlicScript;

class UnlicServiceProvider extends BaseServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->setNamespace('plugins/unlic');

        // Load views
        $this->loadAndPublishViews();

        // Register bypass license check middleware (must be first)
        $this->app['router']->prependMiddlewareToGroup('web', BypassLicenseCheck::class);

        // Register script injection middleware
        $this->app['router']->pushMiddlewareToGroup('web', InjectUnlicScript::class);

        // Add filters for different injection points
        add_filter('base_filter_before_head_close', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        add_filter('base_filter_after_body_tag', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });
    }

    public function register()
    {
        // Register any services if needed
    }
}
