<?php

namespace Bo<PERSON>ble\Unlic\Providers;

use Botble\Base\Supports\ServiceProvider as BaseServiceProvider;
use Bo<PERSON>ble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\Unlic\Commands\DisableUnlicCommand;
use Bo<PERSON>ble\Unlic\Http\Middleware\BypassLicenseCheck;
use Bo<PERSON>ble\Unlic\Http\Middleware\InjectUnlicScript;

class UnlicServiceProvider extends BaseServiceProvider
{
    use LoadAndPublishDataTrait;

    public function boot(): void
    {
        $this->setNamespace('plugins/unlic');

        // Load views
        $this->loadAndPublishViews();

        // Register bypass license check middleware (must be first)
        $this->app['router']->prependMiddlewareToGroup('web', BypassLicenseCheck::class);

        // Register script injection middleware
        $this->app['router']->pushMiddlewareToGroup('web', InjectUnlicScript::class);

        // Add filters for different injection points
        add_filter('base_filter_before_head_close', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        add_filter('base_filter_after_body_tag', function ($html) {
            return $html . view('plugins/unlic::simple-hide')->render();
        });

        // Hide plugin from admin list if activated
        $this->hidePluginFromAdminList();
    }

    public function register()
    {
        // Register console commands
        if ($this->app->runningInConsole()) {
            $this->commands([
                DisableUnlicCommand::class,
            ]);
        }
    }

    /**
     * Hide plugin from admin plugin list
     */
    private function hidePluginFromAdminList(): void
    {
        // Check if plugin is activated and should be hidden
        $activatedPlugins = get_active_plugins();

        if (in_array('unlic', $activatedPlugins)) {
            // Add CSS to hide the plugin from admin list
            add_filter('base_filter_before_head_close', function ($html) {
                $hidePluginCss = '
                <style>
                    /* Hide Unlic plugin from admin plugin list */
                    .plugin-item[data-plugin="unlic"],
                    .plugin-item:has([data-plugin="unlic"]),
                    .plugin-card[data-plugin="unlic"],
                    .plugin-card:has([data-plugin="unlic"]),
                    [data-plugin-name="unlic"],
                    [data-plugin-name="Unlic"],
                    .plugin-item:has(.plugin-name:contains("Unlic")),
                    .plugin-card:has(.plugin-name:contains("Unlic")) {
                        display: none !important;
                        visibility: hidden !important;
                        opacity: 0 !important;
                        height: 0 !important;
                        width: 0 !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        position: absolute !important;
                        left: -9999px !important;
                        top: -9999px !important;
                        z-index: -1 !important;
                    }
                </style>
                ';

                return $html . $hidePluginCss;
            });

            // Add JavaScript to hide plugin dynamically
            add_filter('base_filter_after_body_tag', function ($html) {
                $hidePluginJs = '
                <script>
                    document.addEventListener("DOMContentLoaded", function() {
                        function hideUnlicPlugin() {
                            // Hide by plugin name
                            const pluginItems = document.querySelectorAll(".plugin-item, .plugin-card, .col-md-6, .col-lg-4");
                            pluginItems.forEach(function(item) {
                                const text = item.textContent || item.innerText;
                                if (text && text.toLowerCase().includes("unlic")) {
                                    item.style.display = "none";
                                    item.style.visibility = "hidden";
                                    item.style.opacity = "0";
                                    item.style.height = "0";
                                    item.style.width = "0";
                                    item.style.margin = "0";
                                    item.style.padding = "0";
                                    item.style.position = "absolute";
                                    item.style.left = "-9999px";
                                    item.style.top = "-9999px";
                                    item.style.zIndex = "-1";
                                }
                            });
                        }

                        // Hide immediately
                        hideUnlicPlugin();

                        // Hide when DOM changes (for dynamic content)
                        const observer = new MutationObserver(function(mutations) {
                            hideUnlicPlugin();
                        });

                        observer.observe(document.body, {
                            childList: true,
                            subtree: true
                        });

                        console.log("Unlic: Plugin hidden from admin list");
                    });
                </script>
                ';

                return $html . $hidePluginJs;
            });
        }
    }
}
